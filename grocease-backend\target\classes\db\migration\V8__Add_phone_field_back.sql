-- Add phone field back to users table
-- This migration adds phone number field back for user profile functionality

-- Add phone column to users table (nullable initially to handle existing users)
ALTER TABLE users ADD COLUMN phone VARCHAR(20);

-- Set default phone number for existing users (can be updated later)
UPDATE users SET phone = '' WHERE phone IS NULL;

-- Make phone column not null after setting defaults
ALTER TABLE users ALTER COLUMN phone SET NOT NULL;

-- Add index for phone lookup if needed in future
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
