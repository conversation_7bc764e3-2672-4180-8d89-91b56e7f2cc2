#!/usr/bin/env node

/**
 * Test script to validate the pagination fix for IndexOutOfBoundsException
 * This script tests various pagination scenarios that could cause the error
 */

const http = require('http');

const API_BASE_URL = 'http://localhost:8080/api';

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = http.request(url, {
      timeout: 5000,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testPagination(endpoint, testCases) {
  console.log(`\n🧪 Testing ${endpoint}:`);
  
  for (const testCase of testCases) {
    const { name, params, expectError } = testCase;
    const url = `${API_BASE_URL}${endpoint}?${new URLSearchParams(params).toString()}`;
    
    try {
      console.log(`  Testing ${name}...`);
      const response = await makeRequest(url);
      
      if (expectError && response.status === 200) {
        console.log(`    ⚠️  Expected error but got success: ${response.status}`);
      } else if (!expectError && response.status !== 200) {
        console.log(`    ❌ Unexpected error: ${response.status} - ${response.data.message || 'Unknown error'}`);
      } else if (response.status === 200) {
        console.log(`    ✅ Success: ${response.status}`);
        
        // Validate response structure
        if (response.data.data && response.data.pagination) {
          const { page, limit, total, totalPages } = response.data.pagination;
          console.log(`       Page: ${page}, Limit: ${limit}, Total: ${total}, TotalPages: ${totalPages}`);
          
          // Check for logical consistency
          if (page < 0) {
            console.log(`    ⚠️  Negative page number returned: ${page}`);
          }
          if (limit <= 0) {
            console.log(`    ⚠️  Invalid limit returned: ${limit}`);
          }
          if (totalPages < 0) {
            console.log(`    ⚠️  Negative total pages: ${totalPages}`);
          }
        } else {
          console.log(`    ⚠️  Missing pagination data in response`);
        }
      } else {
        console.log(`    ✅ Expected error: ${response.status}`);
      }
    } catch (error) {
      if (expectError) {
        console.log(`    ✅ Expected error: ${error.message}`);
      } else {
        console.log(`    ❌ Unexpected error: ${error.message}`);
      }
    }
  }
}

async function main() {
  console.log('🔍 Testing Pagination Fix for IndexOutOfBoundsException\n');
  console.log(`Backend URL: ${API_BASE_URL}`);

  // Test cases that could trigger IndexOutOfBoundsException
  const problematicTestCases = [
    {
      name: 'Negative page number',
      params: { page: '-1', limit: '10' },
      expectError: false // Should be handled gracefully now
    },
    {
      name: 'Very negative page number',
      params: { page: '-999', limit: '10' },
      expectError: false // Should be handled gracefully now
    },
    {
      name: 'Zero limit',
      params: { page: '0', limit: '0' },
      expectError: false // Should be handled gracefully now
    },
    {
      name: 'Negative limit',
      params: { page: '0', limit: '-5' },
      expectError: false // Should be handled gracefully now
    },
    {
      name: 'Very large page number',
      params: { page: '999999', limit: '10' },
      expectError: false // Should return empty results
    },
    {
      name: 'Very large limit',
      params: { page: '0', limit: '999999' },
      expectError: false // Should be capped at 100
    },
    {
      name: 'Non-numeric page',
      params: { page: 'abc', limit: '10' },
      expectError: true // Should return 400 error
    },
    {
      name: 'Non-numeric limit',
      params: { page: '0', limit: 'xyz' },
      expectError: true // Should return 400 error
    }
  ];

  // Normal test cases
  const normalTestCases = [
    {
      name: 'Normal pagination',
      params: { page: '0', limit: '10' },
      expectError: false
    },
    {
      name: 'Second page',
      params: { page: '1', limit: '5' },
      expectError: false
    },
    {
      name: 'Small limit',
      params: { page: '0', limit: '1' },
      expectError: false
    }
  ];

  // Test different endpoints
  const endpoints = [
    '/products',
    '/categories',
    '/banners'
  ];

  for (const endpoint of endpoints) {
    await testPagination(endpoint, [...problematicTestCases, ...normalTestCases]);
  }

  // Test authenticated endpoints (these will fail with 401 but shouldn't have IndexOutOfBoundsException)
  console.log('\n🔐 Testing authenticated endpoints (expect 401 errors):');
  
  const authEndpoints = ['/orders'];
  
  for (const endpoint of authEndpoints) {
    console.log(`\n🧪 Testing ${endpoint} (without auth):`);
    
    for (const testCase of problematicTestCases.slice(0, 3)) { // Test just a few cases
      const { name, params } = testCase;
      const url = `${API_BASE_URL}${endpoint}?${new URLSearchParams(params).toString()}`;
      
      try {
        console.log(`  Testing ${name}...`);
        const response = await makeRequest(url);
        
        if (response.status === 401) {
          console.log(`    ✅ Expected 401 Unauthorized: ${response.status}`);
        } else if (response.status === 500) {
          console.log(`    ❌ Server error (possible IndexOutOfBoundsException): ${response.status}`);
          console.log(`       Response: ${JSON.stringify(response.data).substring(0, 200)}...`);
        } else {
          console.log(`    ⚠️  Unexpected status: ${response.status}`);
        }
      } catch (error) {
        console.log(`    ❌ Network error: ${error.message}`);
      }
    }
  }

  console.log('\n📊 Test Summary:');
  console.log('✅ If you see mostly "Success" and "Expected error" messages, the fix is working');
  console.log('❌ If you see "Server error" or "IndexOutOfBoundsException", the issue persists');
  console.log('⚠️  Warnings indicate potential issues but not critical errors');
  
  console.log('\n💡 Next steps:');
  console.log('1. If tests pass: The pagination fix is working correctly');
  console.log('2. If tests fail: Check backend logs for detailed error information');
  console.log('3. Restart the backend server if needed: ./run-app.bat');
}

// Handle errors gracefully
process.on('uncaughtException', (error) => {
  console.log(`\n❌ Unexpected error: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (error) => {
  console.log(`\n❌ Unhandled promise rejection: ${error.message}`);
  process.exit(1);
});

// Run the tests
main().catch((error) => {
  console.log(`\n❌ Test execution failed: ${error.message}`);
  process.exit(1);
});
