import { API_CONFIG } from '../constants';
import { storage } from './storage';
import { ApiError, NetworkError, AuthenticationError, ValidationError, withRetry } from '../utils/errorHandler';
import { apiLogger } from '../utils/apiLogger';

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class HttpClient {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    try {
      // Check if storage is available and has the method
      if (storage && typeof storage.getAuthToken === 'function') {
        const token = await storage.getAuthToken();
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
      } else {
        console.warn('Storage service not available or getAuthToken method missing');
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
      // Continue without token - this allows unauthenticated requests
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = await this.getAuthHeaders();

    const config: RequestInit = {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    };

    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    // Log request details
    const requestId = Math.random().toString(36).substr(2, 9);
    const startTime = Date.now();

    apiLogger.logRequest(
      requestId,
      config.method || 'GET',
      url,
      config.headers as Record<string, string>,
      config.body
    );

    try {
      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      const duration = Date.now() - startTime;

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Log error response
        apiLogger.logResponse(requestId, response.status, response.statusText, url, errorData, duration);

        // Handle different HTTP status codes
        switch (response.status) {
          case 401:
            throw new AuthenticationError(errorData.message || 'Authentication failed');
          case 400:
            throw new ValidationError(errorData.message || 'Validation failed', errorData.data);
          case 404:
            throw new ApiError(errorData.message || 'Resource not found', 404, 'NOT_FOUND');
          case 429:
            throw new ApiError(errorData.message || 'Too many requests', 429, 'RATE_LIMITED');
          case 500:
            throw new ApiError(errorData.message || 'Internal server error', 500, 'SERVER_ERROR');
          default:
            throw new ApiError(
              errorData.message || errorData.error || `HTTP ${response.status}`,
              response.status,
              'API_ERROR'
            );
        }
      }

      const data = await response.json();

      // Log successful response
      apiLogger.logResponse(requestId, response.status, response.statusText, url, data, duration);

      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      const duration = Date.now() - startTime;

      // Log network/timeout errors
      apiLogger.logError(requestId, url, error, duration);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new NetworkError('Request timeout');
        }
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          throw new NetworkError('Network connection failed');
        }
        // Re-throw our custom errors
        if (error instanceof ApiError || error instanceof AuthenticationError || error instanceof ValidationError) {
          throw error;
        }
      }

      throw new NetworkError('Network error');
    }
  }



  async get<T>(endpoint: string, retries: number = API_CONFIG.RETRY_ATTEMPTS): Promise<T> {
    return withRetry(() => this.request<T>(endpoint, { method: 'GET' }), retries);
  }

  async post<T>(endpoint: string, data?: any, retries: number = API_CONFIG.RETRY_ATTEMPTS): Promise<T> {
    return withRetry(() => this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }), retries);
  }

  async put<T>(endpoint: string, data?: any, retries: number = API_CONFIG.RETRY_ATTEMPTS): Promise<T> {
    return withRetry(() => this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }), retries);
  }

  async delete<T>(endpoint: string, retries: number = API_CONFIG.RETRY_ATTEMPTS): Promise<T> {
    return withRetry(() => this.request<T>(endpoint, { method: 'DELETE' }), retries);
  }

  async patch<T>(endpoint: string, data?: any, retries: number = API_CONFIG.RETRY_ATTEMPTS): Promise<T> {
    return withRetry(() => this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    }), retries);
  }

  // Upload file with multipart/form-data
  async uploadFile<T>(endpoint: string, formData: FormData): Promise<T> {
    const token = await storage.getAuthToken();
    const headers: Record<string, string> = {};
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      headers,
      body: formData,
    });
  }
}

export const httpClient = new HttpClient();
