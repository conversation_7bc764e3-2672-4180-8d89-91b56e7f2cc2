/**
 * Centralized API logging utility
 * Provides consistent logging for all API calls with configurable levels
 */

export enum LogLevel {
  NONE = 0,
  ERROR = 1,
  WARN = 2,
  INFO = 3,
  DEBUG = 4,
  VERBOSE = 5
}

interface ApiLogConfig {
  enabled: boolean;
  level: LogLevel;
  logRequests: boolean;
  logResponses: boolean;
  logErrors: boolean;
  logTiming: boolean;
  maxResponseLength: number;
  sensitiveFields: string[];
}

class ApiLogger {
  private config: ApiLogConfig = {
    enabled: true,
    level: LogLevel.VERBOSE,
    logRequests: true,
    logResponses: true,
    logErrors: true,
    logTiming: true,
    maxResponseLength: 1000,
    sensitiveFields: ['password', 'token', 'refreshToken', 'secret', 'key', 'authorization']
  };

  configure(config: Partial<ApiLogConfig>) {
    this.config = { ...this.config, ...config };
  }

  private shouldLog(level: LogLevel): boolean {
    return this.config.enabled && this.config.level >= level;
  }

  private sanitize(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj;
    
    const sanitized = Array.isArray(obj) ? [...obj] : { ...obj };
    
    const sanitizeRecursive = (item: any): any => {
      if (!item || typeof item !== 'object') return item;
      
      if (Array.isArray(item)) {
        return item.map(sanitizeRecursive);
      }
      
      const result = { ...item };
      for (const field of this.config.sensitiveFields) {
        if (result[field]) {
          result[field] = '[REDACTED]';
        }
      }
      
      // Recursively sanitize nested objects
      for (const key in result) {
        if (typeof result[key] === 'object' && result[key] !== null) {
          result[key] = sanitizeRecursive(result[key]);
        }
      }
      
      return result;
    };
    
    return sanitizeRecursive(sanitized);
  }

  private formatHeaders(headers: Record<string, string>): Record<string, string> {
    const formatted = { ...headers };
    if (formatted.Authorization || formatted.authorization) {
      const authKey = formatted.Authorization ? 'Authorization' : 'authorization';
      formatted[authKey] = formatted[authKey].replace(/Bearer .+/, 'Bearer [REDACTED]');
    }
    return formatted;
  }

  logRequest(requestId: string, method: string, url: string, headers?: Record<string, string>, body?: any) {
    if (!this.shouldLog(LogLevel.INFO) || !this.config.logRequests) return;

    const logData = {
      id: requestId,
      method,
      url,
      timestamp: new Date().toISOString(),
      ...(headers && { headers: this.formatHeaders(headers) }),
      ...(body && { body: this.sanitize(body) })
    };

    console.log(`🚀 [API REQUEST ${requestId}]`, logData);
  }

  logResponse(requestId: string, status: number, statusText: string, url: string, data: any, duration: number) {
    if (!this.shouldLog(LogLevel.INFO) || !this.config.logResponses) return;

    const logData = {
      id: requestId,
      status,
      statusText,
      url,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    };

    if (status >= 200 && status < 300) {
      console.log(`✅ [API SUCCESS ${requestId}]`, logData);
      
      // Log response data
      const responseStr = JSON.stringify(data);
      if (responseStr.length > this.config.maxResponseLength) {
        console.log(`📄 [API RESPONSE ${requestId}] (truncated):`, 
          responseStr.substring(0, this.config.maxResponseLength) + '...');
      } else {
        console.log(`📄 [API RESPONSE ${requestId}]:`, this.sanitize(data));
      }
    } else {
      console.log(`❌ [API ERROR ${requestId}]`, { ...logData, errorData: this.sanitize(data) });
    }
  }

  logError(requestId: string, url: string, error: any, duration: number) {
    if (!this.shouldLog(LogLevel.ERROR) || !this.config.logErrors) return;

    console.log(`💥 [API NETWORK ERROR ${requestId}]`, {
      id: requestId,
      url,
      duration: `${duration}ms`,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.name : 'Unknown',
      timestamp: new Date().toISOString()
    });
  }

  logAuth(action: string, email: string, success: boolean, message?: string, hasToken?: boolean) {
    if (!this.shouldLog(LogLevel.INFO)) return;

    const emoji = action === 'login' ? '🔐' : action === 'register' ? '📝' : '🔑';
    console.log(`${emoji} [AUTH] ${action.toUpperCase()}`, {
      email,
      success,
      message,
      hasToken,
      timestamp: new Date().toISOString()
    });
  }

  logApiCall(service: string, action: string, params?: any, result?: any) {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    console.log(`📡 [${service.toUpperCase()}] ${action}`, {
      params: params ? this.sanitize(params) : undefined,
      result: result ? { 
        success: result.success, 
        dataLength: result.data ? (Array.isArray(result.data) ? result.data.length : 'object') : 0 
      } : undefined,
      timestamp: new Date().toISOString()
    });
  }

  // Quick configuration presets
  enableVerboseLogging() {
    this.configure({
      enabled: true,
      level: LogLevel.VERBOSE,
      logRequests: true,
      logResponses: true,
      logErrors: true,
      logTiming: true
    });
  }

  enableErrorsOnly() {
    this.configure({
      enabled: true,
      level: LogLevel.ERROR,
      logRequests: false,
      logResponses: false,
      logErrors: true,
      logTiming: false
    });
  }

  disableLogging() {
    this.configure({ enabled: false });
  }
}

// Export singleton instance
export const apiLogger = new ApiLogger();

// Enable verbose logging by default in development
if (__DEV__) {
  apiLogger.enableVerboseLogging();
  console.log('🔧 [API LOGGER] Verbose logging enabled for development');
}
