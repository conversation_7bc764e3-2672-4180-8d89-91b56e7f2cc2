{"name": "grocease", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clear": "expo start --clear", "tunnel": "expo start --tunnel", "build:android": "expo build:android", "build:ios": "expo build:ios", "validate-api": "node validate-integration.js", "test-api": "node validate-integration.js", "test-pagination": "node test-pagination-fix.js"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@tanstack/react-query": "^5.81.5", "expo": "~53.0.17", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-keychain": "^10.0.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-react-jsx": "^7.27.1", "@types/react": "~19.0.10", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}