import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
  timestamp?: number;
}

interface UseLocationReturn {
  location: LocationData | null;
  loading: boolean;
  error: string | null;
  requestLocation: () => Promise<void>;
  hasLocationPermission: boolean;
}

const LOCATION_STORAGE_KEY = 'user_location';
const LOCATION_PERMISSION_KEY = 'location_permission_status';
const LOCATION_CACHE_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds

export const useLocation = (): UseLocationReturn => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);

  // Load cached location and permission status
  const loadCachedData = async () => {
    try {
      // Load cached location
      const cachedLocationStr = await AsyncStorage.getItem(LOCATION_STORAGE_KEY);
      if (cachedLocationStr) {
        const cachedLocation: LocationData = JSON.parse(cachedLocationStr);
        const now = Date.now();

        // Check if cached location is still valid (within cache duration)
        if (cachedLocation.timestamp && (now - cachedLocation.timestamp) < LOCATION_CACHE_DURATION) {
          setLocation(cachedLocation);
        }
      }

      // Load permission status
      const permissionStatus = await AsyncStorage.getItem(LOCATION_PERMISSION_KEY);
      if (permissionStatus === 'granted') {
        setHasLocationPermission(true);

        // If we have permission but no valid cached location, request new location
        if (!location || !cachedLocationStr) {
          await requestLocation();
        }
      }
    } catch (error) {
      console.error('Error loading cached location data:', error);
    }
  };

  // Save location to cache
  const saveLocationToCache = async (locationData: LocationData) => {
    try {
      const locationWithTimestamp = {
        ...locationData,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem(LOCATION_STORAGE_KEY, JSON.stringify(locationWithTimestamp));
    } catch (error) {
      console.error('Error saving location to cache:', error);
    }
  };

  // Save permission status
  const savePermissionStatus = async (status: string) => {
    try {
      await AsyncStorage.setItem(LOCATION_PERMISSION_KEY, status);
      setHasLocationPermission(status === 'granted');
    } catch (error) {
      console.error('Error saving permission status:', error);
    }
  };

  const requestLocation = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check current permission status
      const { status: currentStatus } = await Location.getForegroundPermissionsAsync();

      let permissionStatus = currentStatus;

      // Request permission if not already granted
      if (currentStatus !== 'granted') {
        const { status } = await Location.requestForegroundPermissionsAsync();
        permissionStatus = status;
      }

      // Save permission status
      await savePermissionStatus(permissionStatus);

      if (permissionStatus !== 'granted') {
        setError('Permission to access location was denied');
        return;
      }

      // Get current position
      const position = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Reverse geocode to get address
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
      });

      let locationData: LocationData;

      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        locationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          address: `${address.street || ''} ${address.streetNumber || ''}`.trim(),
          city: address.city || 'Unknown City',
          state: address.region || 'Unknown State',
        };
      } else {
        // Fallback if reverse geocoding fails
        locationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          address: 'Current Location',
          city: 'San Francisco',
          state: 'CA',
        };
      }

      setLocation(locationData);
      await saveLocationToCache(locationData);

    } catch (err) {
      console.error('Location error:', err);
      setError('Failed to get location');

      // Set a default location for demo purposes
      const defaultLocation: LocationData = {
        latitude: 37.7749,
        longitude: -122.4194,
        address: '123 Main Street',
        city: 'San Francisco',
        state: 'CA',
      };

      setLocation(defaultLocation);
      await saveLocationToCache(defaultLocation);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCachedData();
  }, []);

  return {
    location,
    loading,
    error,
    requestLocation,
    hasLocationPermission,
  };
};
