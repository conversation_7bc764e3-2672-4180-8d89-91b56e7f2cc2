$body = @{
    name = "Test User"
    email = "<EMAIL>"
    phone = "1234567890"
    password = "password123"
    confirmPassword = "password123"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/register" -Method Post -Body $body -ContentType "application/json"
    Write-Host "Registration successful:"
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Registration failed:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
