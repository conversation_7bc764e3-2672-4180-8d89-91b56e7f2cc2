declare function _exports(context: any, components: any, utils: any): {
    VariableDeclarator(node: any): void;
    FunctionDeclaration: (node: ASTNode) => void;
    ArrowFunctionExpression: (node: ASTNode) => void;
    FunctionExpression: (node: ASTNode) => void;
    'FunctionDeclaration:exit': () => void;
    'ArrowFunctionExpression:exit': () => void;
    'FunctionExpression:exit': () => void;
    JSXSpreadAttribute(node: any): void;
    'MemberExpression, OptionalMemberExpression'(node: any): void;
    ObjectPattern(node: any): void;
    'Program:exit'(): void;
};
export = _exports;
//# sourceMappingURL=usedPropTypes.d.ts.map