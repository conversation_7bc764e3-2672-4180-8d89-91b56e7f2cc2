{"name": "@hookform/resolvers/typanion", "amdName": "hookformResolversTypanion", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: typanion", "main": "dist/typanion.js", "module": "dist/typanion.module.js", "umd:main": "dist/typanion.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.0.0", "@hookform/resolvers": "^2.0.0", "typanion": "^3.3.2"}}