{"name": "@hookform/resolvers/effect-ts", "amdName": "hookformResolversEffectTs", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: effect-ts", "main": "dist/effect-ts.js", "module": "dist/effect-ts.module.js", "umd:main": "dist/effect-ts.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"@hookform/resolvers": "^2.0.0", "effect": "^3.10.3", "react-hook-form": "^7.0.0"}}