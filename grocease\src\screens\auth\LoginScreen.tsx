import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useAuth } from '../../hooks/useAuth';
import { validateEmail } from '../../utils';
import Button from '../../components/Button';

const LoginScreen = () => {
  const navigation = useNavigation();
  const { login } = useAuth();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLogin = async () => {
    // Validation
    if (!formData.email.trim()) {
      Alert.alert('Validation Error', 'Please enter your email address');
      return;
    }

    if (!validateEmail(formData.email)) {
      Alert.alert('Validation Error', 'Please enter a valid email address');
      return;
    }

    if (!formData.password.trim()) {
      Alert.alert('Validation Error', 'Please enter your password');
      return;
    }

    if (formData.password.length < 6) {
      Alert.alert('Validation Error', 'Password must be at least 6 characters');
      return;
    }

    setIsLoading(true);
    try {
      const result = await login({
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
      });

      if (result.success) {
        // Navigation will be handled by the auth state change
      } else {
        Alert.alert('Login Failed', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword' as never);
  };

  const handleSignUp = () => {
    navigation.navigate('Register' as never);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="px-6 py-8">
            {/* Header */}
            <View className="items-center mb-8">
              <View className="w-20 h-20 bg-primary-500 rounded-2xl items-center justify-center mb-4">
                <Ionicons name="basket" size={40} color="#ffffff" />
              </View>
              <Text className="text-3xl font-bold text-neutral-800 mb-2">
                Welcome Back
              </Text>
              <Text className="text-base text-neutral-600 text-center">
                Sign in to your account to continue shopping
              </Text>
            </View>



            {/* Login Form */}
            <View className="space-y-4">
              {/* Email Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Email Address
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Enter your email"
                    placeholderTextColor="#94a3b8"
                    value={formData.email}
                    onChangeText={(value) => handleInputChange('email', value)}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <View className="absolute right-4 top-3">
                    <Ionicons name="mail" size={20} color="#94a3b8" />
                  </View>
                </View>
              </View>

              {/* Password Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Password
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Enter your password"
                    placeholderTextColor="#94a3b8"
                    value={formData.password}
                    onChangeText={(value) => handleInputChange('password', value)}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    className="absolute right-4 top-3"
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Ionicons 
                      name={showPassword ? "eye-off" : "eye"} 
                      size={20} 
                      color="#94a3b8" 
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Forgot Password */}
              <View className="items-end">
                <TouchableOpacity onPress={handleForgotPassword}>
                  <Text className="text-primary-600 font-semibold">
                    Forgot Password?
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Login Button */}
              <View className="mt-6">
                <Button
                  title="Sign In"
                  onPress={handleLogin}
                  loading={isLoading}
                  size="lg"
                  fullWidth
                />
              </View>

              {/* Divider */}
              <View className="flex-row items-center my-6">
                <View className="flex-1 h-px bg-neutral-200" />
                <Text className="px-4 text-neutral-500">or</Text>
                <View className="flex-1 h-px bg-neutral-200" />
              </View>

              {/* Social Login Buttons */}
              <View className="space-y-3">
                <Button
                  title="Continue with Google"
                  variant="outline"
                  size="lg"
                  fullWidth
                  onPress={() => Alert.alert('Coming Soon', 'Google sign-in will be available soon!')}
                />
                <Button
                  title="Continue with Apple"
                  variant="outline"
                  size="lg"
                  fullWidth
                  onPress={() => Alert.alert('Coming Soon', 'Apple sign-in will be available soon!')}
                />
              </View>

              {/* Sign Up Link */}
              <View className="flex-row justify-center items-center mt-8">
                <Text className="text-neutral-600">
                  Don't have an account? 
                </Text>
                <TouchableOpacity onPress={handleSignUp} className="ml-1">
                  <Text className="text-primary-600 font-semibold">
                    Sign Up
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
