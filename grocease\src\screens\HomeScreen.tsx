import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { Category, Banner } from '../types';
import { useLocation } from '../hooks/useLocation';
import { useCart } from '../hooks/useCart';
import { useAlert } from '../hooks/useCustomAlert';

import SearchBar from '../components/SearchBar';
import CategoryCard from '../components/CategoryCard';
import AutoScrollBanner from '../components/AutoScrollBanner';
import LoadingSpinner from '../components/LoadingSpinner';
import Button from '../components/Button';
import ProductCard from '../components/ProductCard';

const HomeScreen = () => {
  const navigation = useNavigation();
  const { location, loading: locationLoading, requestLocation, hasLocationPermission } = useLocation();
  const { items } = useCart();
  const { alert, confirm } = useAlert();

  const cartItemCount = items.reduce((count, item) => count + item.quantity, 0);

  // Fetch categories
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    refetch: refetchCategories
  } = useQuery({
    queryKey: ['categories'],
    queryFn: api.getCategories,
  });

  // Fetch banners
  const {
    data: bannersData,
    isLoading: bannersLoading,
    refetch: refetchBanners
  } = useQuery({
    queryKey: ['banners'],
    queryFn: api.getBanners,
  });

  // Fetch popular products
  const {
    data: popularProductsData,
    isLoading: popularProductsLoading,
    refetch: refetchPopularProducts
  } = useQuery({
    queryKey: ['popularProducts'],
    queryFn: () => api.getPopularProducts(6),
  });

  // Fetch new products
  const {
    data: newProductsData,
    isLoading: newProductsLoading,
    refetch: refetchNewProducts
  } = useQuery({
    queryKey: ['newProducts'],
    queryFn: () => api.getNewProducts(6),
  });

  // Fetch featured products
  const {
    data: featuredProductsData,
    isLoading: featuredProductsLoading,
    refetch: refetchFeaturedProducts
  } = useQuery({
    queryKey: ['featuredProducts'],
    queryFn: () => api.getFeaturedProducts(6),
  });

  const isLoading = categoriesLoading || bannersLoading;

  const handleRefresh = async () => {
    await Promise.all([
      refetchCategories(),
      refetchBanners(),
      refetchPopularProducts(),
      refetchNewProducts(),
      refetchFeaturedProducts(),
      requestLocation()
    ]);
  };

  const handleCategoryPress = (category: Category) => {
    navigation.navigate('ProductList' as never, {
      categoryId: category.id,
      categoryName: category.name
    } as never);
  };

  const handleSearchPress = () => {
    navigation.navigate('Search' as never);
  };

  const handleCartPress = () => {
    navigation.navigate('Cart' as never);
  };

  const handleLocationPress = () => {
    if (location) {
      confirm(
        'Current Location',
        `${location.address}\n${location.city}, ${location.state}`,
        () => {
          // Offer options to change location
          confirm(
            'Location Options',
            'How would you like to update your location?',
            requestLocation,
            () => navigation.navigate('ManageAddresses' as never),
            'Use Current Location',
            'Manage Addresses'
          );
        },
        undefined,
        'Change Location',
        'OK'
      );
    } else if (hasLocationPermission) {
      // Has permission but no location, try to get it
      requestLocation();
    } else {
      // No permission, explain and request
      confirm(
        'Location Access',
        'We need location access to show nearby stores and delivery options. Would you like to enable location services?',
        requestLocation,
        () => navigation.navigate('ManageAddresses' as never),
        'Enable Location',
        'Set Manually'
      );
    }
  };

  const handleProductPress = (product: any) => {
    navigation.navigate('ProductDetails' as never, {
      productId: product.id
    } as never);
  };

  const handleAddToCart = (product: any) => {
    // This will be handled by the ProductCard component
  };

  const handleViewAllProducts = (type: 'popular' | 'new' | 'featured') => {
    // Navigate to a filtered product list
    navigation.navigate('ProductList' as never, {
      categoryId: '',
      categoryName: type === 'popular' ? 'Popular Products' : type === 'new' ? 'New Products' : 'Featured Products'
    } as never);
  };

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <View className="w-1/3 p-2">
      <CategoryCard category={item} onPress={handleCategoryPress} />
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      {/* Header */}
      <View className="bg-white px-4 py-4 shadow-sm">
        <View className="flex-row items-center justify-between mb-4">
          <View className="flex-1">
            <Text className="text-sm text-neutral-600">Deliver to</Text>
            <TouchableOpacity
              className="flex-row items-center"
              onPress={handleLocationPress}
            >
              <Ionicons name="location" size={16} color="#22c55e" />
              <Text className="text-base font-semibold text-neutral-800 ml-1 flex-1">
                {locationLoading
                  ? 'Getting location...'
                  : location
                    ? `${location.city}, ${location.state}`
                    : hasLocationPermission
                      ? 'Tap to get location'
                      : 'Set location'
                }
              </Text>
              <Ionicons name="chevron-down" size={16} color="#64748b" />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            className="relative ml-4"
            onPress={handleCartPress}
          >
            <Ionicons name="bag" size={24} color="#1e293b" />
            {cartItemCount > 0 && (
              <View className="absolute -top-2 -right-2 bg-primary-500 rounded-full w-5 h-5 items-center justify-center">
                <Text className="text-xs font-bold text-white">
                  {cartItemCount > 9 ? '9+' : cartItemCount}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <SearchBar
          onPress={handleSearchPress}
          editable={false}
        />
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Banners */}
        {bannersData?.data && bannersData.data.length > 0 && (
          <View className="mt-6">
            <AutoScrollBanner
              banners={bannersData.data}
              autoScrollInterval={4000}
              onBannerPress={(banner) => {
                // Handle banner press - could navigate to specific category or product
                console.log('Banner pressed:', banner.title);
              }}
            />
          </View>
        )}

        {/* Categories Section */}
        <View className="mt-8 px-4">
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-xl font-bold text-neutral-800">
              Shop by Category
            </Text>
            <TouchableOpacity>
              <Text className="text-primary-500 font-semibold">
                See All
              </Text>
            </TouchableOpacity>
          </View>

          {categoriesLoading ? (
            <LoadingSpinner message="Loading categories..." />
          ) : categoriesData?.data ? (
            <FlatList
              data={categoriesData.data}
              renderItem={renderCategoryItem}
              keyExtractor={(item) => item.id}
              numColumns={3}
              scrollEnabled={false}
            />
          ) : (
            <View className="py-8 items-center">
              <Text className="text-neutral-600 mb-4">
                Failed to load categories
              </Text>
              <Button
                title="Retry"
                onPress={() => refetchCategories()}
                variant="outline"
                size="sm"
              />
            </View>
          )}
        </View>

        {/* Featured Products */}
        {featuredProductsData?.data && featuredProductsData.data.length > 0 && (
          <View className="mt-8">
            <View className="flex-row items-center justify-between px-4 mb-4">
              <Text className="text-xl font-bold text-neutral-800">
                🔥 Hot Deals
              </Text>
              <TouchableOpacity onPress={() => handleViewAllProducts('featured')}>
                <Text className="text-primary-600 font-medium">View All</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={featuredProductsData.data}
              renderItem={({ item }) => (
                <ProductCard
                  product={item}
                  onPress={handleProductPress}
                  onAddToCart={handleAddToCart}
                />
              )}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 16 }}
            />
          </View>
        )}

        {/* Popular Products */}
        {popularProductsData?.data && popularProductsData.data.length > 0 && (
          <View className="mt-8">
            <View className="flex-row items-center justify-between px-4 mb-4">
              <Text className="text-xl font-bold text-neutral-800">
                ⭐ Popular Products
              </Text>
              <TouchableOpacity onPress={() => handleViewAllProducts('popular')}>
                <Text className="text-primary-600 font-medium">View All</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={popularProductsData.data}
              renderItem={({ item }) => (
                <ProductCard
                  product={item}
                  onPress={handleProductPress}
                  onAddToCart={handleAddToCart}
                />
              )}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 16 }}
            />
          </View>
        )}

        {/* New Products */}
        {newProductsData?.data && newProductsData.data.length > 0 && (
          <View className="mt-8">
            <View className="flex-row items-center justify-between px-4 mb-4">
              <Text className="text-xl font-bold text-neutral-800">
                ✨ New Arrivals
              </Text>
              <TouchableOpacity onPress={() => handleViewAllProducts('new')}>
                <Text className="text-primary-600 font-medium">View All</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={newProductsData.data}
              renderItem={({ item }) => (
                <ProductCard
                  product={item}
                  onPress={handleProductPress}
                  onAddToCart={handleAddToCart}
                />
              )}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 16 }}
            />
          </View>
        )}

        {/* Quick Actions */}
        <View className="mt-8 px-4 pb-8">
          <Text className="text-xl font-bold text-neutral-800 mb-4">
            Quick Actions
          </Text>

          <View className="flex-row space-x-4">
            <TouchableOpacity className="flex-1 bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 items-center">
              <View className="w-12 h-12 bg-accent-100 rounded-2xl items-center justify-center mb-2">
                <Ionicons name="flash" size={24} color="#f59e0b" />
              </View>
              <Text className="text-sm font-semibold text-neutral-800">
                Express Delivery
              </Text>
              <Text className="text-xs text-neutral-600 text-center">
                Under 30 mins
              </Text>
            </TouchableOpacity>

            <TouchableOpacity className="flex-1 bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 items-center">
              <View className="w-12 h-12 bg-blue-100 rounded-2xl items-center justify-center mb-2">
                <Ionicons name="gift" size={24} color="#3b82f6" />
              </View>
              <Text className="text-sm font-semibold text-neutral-800">
                Deals & Offers
              </Text>
              <Text className="text-xs text-neutral-600 text-center">
                Save more
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;
