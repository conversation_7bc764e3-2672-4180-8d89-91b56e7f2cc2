$token = "eyJhbGciOiJIUzUxMiJ9.********************************************************************************.ujLBl3BcquRLy-ju5zw0mvBoMtyQUob3SlTin-94p7vcYoLGeeUoejGPP91wqSxubf26rX-EQXFCebJNXnDw3g"

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/users/profile" -Method Get -Headers $headers
    Write-Host "Profile retrieved successfully:"
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Profile retrieval failed:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
