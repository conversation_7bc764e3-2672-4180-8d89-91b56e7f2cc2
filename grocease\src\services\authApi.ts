import {
  LoginCredentials,
  RegisterData,
  AuthResponse,
  ForgotPasswordData,
  ResetPasswordData,
  OTPVerificationData,
  AuthUser,
  ApiResponse
} from '../types';
import { httpClient } from './httpClient';
import { createErrorResponse } from '../utils/errorHandler';
import { apiLogger } from '../utils/apiLogger';

export const authApi = {
  // Login
  login: async (credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> => {
    try {
      const response = await httpClient.post<ApiResponse<AuthResponse>>('/auth/login', credentials);
      apiLogger.logAuth('login', credentials.email, response.success, response.message, !!response.data?.token);
      return response;
    } catch (error) {
      apiLogger.logAuth('login', credentials.email, false, error instanceof Error ? error.message : 'Unknown error');
      return createErrorResponse<AuthResponse>(error);
    }
  },

  // Register
  register: async (userData: RegisterData): Promise<ApiResponse<AuthResponse>> => {
    try {
      const response = await httpClient.post<ApiResponse<AuthResponse>>('/auth/register', userData);
      apiLogger.logAuth('register', userData.email, response.success, response.message, !!response.data?.token);
      return response;
    } catch (error) {
      apiLogger.logAuth('register', userData.email, false, error instanceof Error ? error.message : 'Registration failed');
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Registration failed'
      };
    }
  },

  // Forgot Password
  forgotPassword: async (data: ForgotPasswordData): Promise<ApiResponse<{ message: string }>> => {
    try {
      const response = await httpClient.post<ApiResponse<string>>('/auth/forgot-password', data);
      return {
        data: { message: response.data },
        success: response.success,
        message: response.message
      };
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send reset email'
      };
    }
  },

  // Verify OTP
  verifyOTP: async (data: OTPVerificationData): Promise<ApiResponse<{ token?: string }>> => {
    try {
      const response = await httpClient.post<ApiResponse<{ token?: string }>>('/auth/verify-otp', data);
      return response;
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'OTP verification failed'
      };
    }
  },

  // Reset Password
  resetPassword: async (data: ResetPasswordData): Promise<ApiResponse<{ message: string }>> => {
    try {
      const response = await httpClient.post<ApiResponse<string>>('/auth/reset-password', data);
      return {
        data: { message: response.data },
        success: response.success,
        message: response.message
      };
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Password reset failed'
      };
    }
  },

  // Refresh Token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
    try {
      const response = await httpClient.post<ApiResponse<{ token: string; refreshToken: string }>>('/auth/refresh-token', { refreshToken });
      return response;
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Token refresh failed'
      };
    }
  },

  // Get current user
  getCurrentUser: async (): Promise<ApiResponse<AuthUser>> => {
    try {
      const response = await httpClient.get<ApiResponse<AuthUser>>('/users/profile');
      return response;
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get user data'
      };
    }
  },

  // Resend OTP
  resendOTP: async (email: string, type: string): Promise<ApiResponse<{ message: string }>> => {
    try {
      const response = await httpClient.post<ApiResponse<string>>('/auth/resend-otp', { email, type });
      return {
        data: { message: response.data },
        success: response.success,
        message: response.message
      };
    } catch (error) {
      return {
        data: null as any,
        success: false,
        message: error instanceof Error ? error.message : 'Failed to resend OTP'
      };
    }
  }
};
