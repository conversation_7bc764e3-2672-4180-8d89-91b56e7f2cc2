import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
  TextInput,
  Alert,
  Modal
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../hooks/useTheme';
import { useAlert } from '../hooks/useCustomAlert';
import { api } from '../services/api';
import { Address, CreateAddressRequest } from '../types';
import Button from '../components/Button';
import LoadingSpinner from '../components/LoadingSpinner';

interface AddressForm {
  type: 'HOME' | 'WORK' | 'OTHER';
  street: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
}

const ManageAddressesScreen = () => {
  const navigation = useNavigation();
  const { isDark } = useTheme();
  const { alert, confirm, destructive } = useAlert();
  const queryClient = useQueryClient();

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [form, setForm] = useState<AddressForm>({
    type: 'HOME',
    street: '',
    city: '',
    state: '',
    zipCode: '',
    isDefault: false
  });

  // Fetch addresses
  const { data: addressesData, isLoading, error } = useQuery({
    queryKey: ['addresses'],
    queryFn: api.getUserAddresses,
  });

  // Create address mutation
  const createAddressMutation = useMutation({
    mutationFn: api.createAddress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['addresses'] });
      setShowAddForm(false);
      resetForm();
      Alert.alert('Success', 'Address added successfully');
    },
    onError: (error: any) => {
      Alert.alert('Error', error?.message || 'Failed to add address');
    }
  });

  // Update address mutation
  const updateAddressMutation = useMutation({
    mutationFn: ({ addressId, data }: { addressId: string; data: Partial<CreateAddressRequest> }) =>
      api.updateAddress(addressId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['addresses'] });
      setEditingAddress(null);
      resetForm();
      Alert.alert('Success', 'Address updated successfully');
    },
    onError: (error: any) => {
      Alert.alert('Error', error?.message || 'Failed to update address');
    }
  });

  // Delete address mutation
  const deleteAddressMutation = useMutation({
    mutationFn: api.deleteAddress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['addresses'] });
      Alert.alert('Success', 'Address deleted successfully');
    },
    onError: (error: any) => {
      Alert.alert('Error', error?.message || 'Failed to delete address');
    }
  });

  const resetForm = () => {
    setForm({
      type: 'HOME',
      street: '',
      city: '',
      state: '',
      zipCode: '',
      isDefault: false
    });
  };

  const handleAddAddress = () => {
    resetForm();
    setEditingAddress(null);
    setShowAddForm(true);
  };

  const handleEditAddress = (address: Address) => {
    setForm({
      type: address.type,
      street: address.street,
      city: address.city,
      state: address.state,
      zipCode: address.zipCode,
      isDefault: address.isDefault || false
    });
    setEditingAddress(address);
    setShowAddForm(true);
  };

  const handleDeleteAddress = (addressId: string) => {
    destructive(
      'Delete Address',
      'Are you sure you want to delete this address?',
      () => {
        deleteAddressMutation.mutate(addressId);
      },
      undefined,
      'Delete',
      'Cancel'
    );
  };

  const handleSetDefault = (addressId: string) => {
    updateAddressMutation.mutate({
      addressId,
      data: { isDefault: true }
    });
  };

  const validateForm = (): boolean => {
    if (!form.street.trim()) {
      Alert.alert('Validation Error', 'Please enter street address');
      return false;
    }
    if (!form.city.trim()) {
      Alert.alert('Validation Error', 'Please enter city');
      return false;
    }
    if (!form.state.trim()) {
      Alert.alert('Validation Error', 'Please enter state');
      return false;
    }
    if (!form.zipCode.trim()) {
      Alert.alert('Validation Error', 'Please enter zip code');
      return false;
    }
    return true;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    if (editingAddress) {
      updateAddressMutation.mutate({
        addressId: editingAddress.id,
        data: form
      });
    } else {
      createAddressMutation.mutate(form);
    }
  };

  const getAddressIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'home':
        return 'home';
      case 'work':
        return 'business';
      default:
        return 'location';
    }
  };

  const renderFormField = (label: string, field: keyof AddressForm, placeholder: string, keyboardType: any = 'default') => (
    <View className="mb-4">
      <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-neutral-200' : 'text-neutral-700'}`}>
        {label}
      </Text>
      <TextInput
        className={`border rounded-xl px-4 py-3 text-base ${
          isDark
            ? 'border-neutral-600 bg-neutral-700 text-neutral-100'
            : 'border-neutral-300 bg-white text-neutral-800'
        }`}
        placeholder={placeholder}
        placeholderTextColor={isDark ? '#a3a3a3' : '#9ca3af'}
        value={field === 'isDefault' ? '' : String(form[field])}
        onChangeText={(text) => setForm(prev => ({ ...prev, [field]: text }))}
        keyboardType={keyboardType}
      />
    </View>
  );

  const renderAddressForm = () => (
    <Modal
      visible={showAddForm}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView className={`flex-1 ${isDark ? 'bg-neutral-900' : 'bg-neutral-50'}`}>
        {/* Header */}
        <View className={`px-4 py-4 border-b ${isDark ? 'bg-neutral-800 border-neutral-700' : 'bg-white border-neutral-200'}`}>
          <View className="flex-row items-center justify-between">
            <TouchableOpacity
              onPress={() => {
                setShowAddForm(false);
                setEditingAddress(null);
                resetForm();
              }}
              className="w-10 h-10 items-center justify-center"
            >
              <Ionicons name="close" size={24} color={isDark ? '#f5f5f5' : '#1e293b'} />
            </TouchableOpacity>
            <Text className={`text-lg font-bold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
              {editingAddress ? 'Edit Address' : 'Add New Address'}
            </Text>
            <View className="w-10" />
          </View>
        </View>

        <ScrollView className="flex-1 px-4 py-6" showsVerticalScrollIndicator={false}>
          {/* Address Type */}
          <View className="mb-4">
            <Text className={`text-sm font-medium mb-2 ${isDark ? 'text-neutral-200' : 'text-neutral-700'}`}>
              Address Type
            </Text>
            <View className="flex-row space-x-3">
              {(['HOME', 'WORK', 'OTHER'] as const).map((type) => (
                <TouchableOpacity
                  key={type}
                  className={`flex-1 py-3 px-4 rounded-xl border-2 ${
                    form.type === type
                      ? 'border-primary-500 bg-primary-50'
                      : isDark
                        ? 'border-neutral-600 bg-neutral-700'
                        : 'border-neutral-300 bg-white'
                  }`}
                  onPress={() => setForm(prev => ({ ...prev, type }))}
                >
                  <Text className={`text-center font-medium ${
                    form.type === type
                      ? 'text-primary-700'
                      : isDark
                        ? 'text-neutral-200'
                        : 'text-neutral-700'
                  }`}>
                    {type.charAt(0) + type.slice(1).toLowerCase()}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {renderFormField('Street Address', 'street', 'Enter your street address')}

          <View className="flex-row space-x-3">
            <View className="flex-1">
              {renderFormField('City', 'city', 'City')}
            </View>
            <View className="w-20">
              {renderFormField('State', 'state', 'State')}
            </View>
          </View>

          {renderFormField('ZIP Code', 'zipCode', 'ZIP Code', 'numeric')}

          {/* Default Address Toggle */}
          <TouchableOpacity
            className={`flex-row items-center justify-between p-4 rounded-xl border ${
              isDark ? 'border-neutral-600 bg-neutral-700' : 'border-neutral-300 bg-white'
            }`}
            onPress={() => setForm(prev => ({ ...prev, isDefault: !prev.isDefault }))}
          >
            <Text className={`font-medium ${isDark ? 'text-neutral-200' : 'text-neutral-700'}`}>
              Set as default address
            </Text>
            <View className={`w-6 h-6 rounded border-2 items-center justify-center ${
              form.isDefault
                ? 'border-primary-500 bg-primary-500'
                : isDark
                  ? 'border-neutral-500'
                  : 'border-neutral-400'
            }`}>
              {form.isDefault && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
          </TouchableOpacity>
        </ScrollView>

        {/* Bottom Action */}
        <View className={`px-4 py-4 border-t ${isDark ? 'bg-neutral-800 border-neutral-700' : 'bg-white border-neutral-200'}`}>
          <Button
            title={editingAddress ? 'Update Address' : 'Add Address'}
            onPress={handleSubmit}
            loading={createAddressMutation.isPending || updateAddressMutation.isPending}
            size="lg"
            fullWidth
          />
        </View>
      </SafeAreaView>
    </Modal>
  );

  const renderAddressItem = ({ item }: { item: Address }) => (
    <View className={`rounded-2xl p-4 mb-3 ${isDark ? 'bg-neutral-800' : 'bg-white'} shadow-sm border ${isDark ? 'border-neutral-700' : 'border-neutral-100'}`}>
      <View className="flex-row items-start justify-between mb-3">
        <View className="flex-row items-center flex-1">
          <View className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${isDark ? 'bg-neutral-700' : 'bg-neutral-100'}`}>
            <Ionicons 
              name={getAddressIcon(item.type) as any} 
              size={20} 
              color={isDark ? '#d4d4d4' : '#64748b'} 
            />
          </View>
          <View className="flex-1">
            <View className="flex-row items-center">
              <Text className={`text-lg font-semibold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
                {item.type.charAt(0) + item.type.slice(1).toLowerCase()}
              </Text>
              {item.isDefault && (
                <View className="ml-2 px-2 py-1 bg-primary-100 rounded-full">
                  <Text className="text-primary-700 text-xs font-medium">Default</Text>
                </View>
              )}
            </View>
            <Text className={`text-sm mt-1 ${isDark ? 'text-neutral-300' : 'text-neutral-600'}`}>
              {item.street}
            </Text>
            <Text className={`text-sm ${isDark ? 'text-neutral-400' : 'text-neutral-500'}`}>
              {item.city}, {item.state} {item.zipCode}
            </Text>
          </View>
        </View>
      </View>

      <View className="flex-row space-x-2">
        {!item.isDefault && (
          <TouchableOpacity
            onPress={() => handleSetDefault(item.id)}
            className={`flex-1 py-2 px-3 rounded-lg border ${isDark ? 'border-neutral-600 bg-neutral-700' : 'border-neutral-300 bg-neutral-50'}`}
          >
            <Text className={`text-center text-sm font-medium ${isDark ? 'text-neutral-200' : 'text-neutral-700'}`}>
              Set Default
            </Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          onPress={() => handleEditAddress(item)}
          className="flex-1 py-2 px-3 rounded-lg bg-primary-100 border border-primary-200"
        >
          <Text className="text-center text-sm font-medium text-primary-700">
            Edit
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={() => handleDeleteAddress(item.id)}
          className={`py-2 px-3 rounded-lg border ${isDark ? 'border-error-800 bg-error-900/20' : 'border-error-200 bg-error-50'}`}
        >
          <Ionicons name="trash-outline" size={16} color="#ef4444" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const addresses = addressesData?.data || [];

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-neutral-900' : 'bg-neutral-50'}`}>
      {/* Header */}
      <View className={`px-4 py-4 border-b ${isDark ? 'bg-neutral-800 border-neutral-700' : 'bg-white border-neutral-200'}`}>
        <View className="flex-row items-center justify-between">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="w-10 h-10 items-center justify-center"
          >
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={isDark ? '#f5f5f5' : '#1e293b'} 
            />
          </TouchableOpacity>
          <Text className={`text-lg font-bold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
            Manage Addresses
          </Text>
          <TouchableOpacity
            onPress={handleAddAddress}
            className="w-10 h-10 items-center justify-center"
          >
            <Ionicons 
              name="add" 
              size={24} 
              color={isDark ? '#f5f5f5' : '#1e293b'} 
            />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1 px-4 py-6" showsVerticalScrollIndicator={false}>
        {/* Info Card */}
        <View className={`rounded-2xl p-4 mb-6 ${isDark ? 'bg-info-900/20 border-info-800' : 'bg-info-50 border-info-200'} border`}>
          <View className="flex-row items-start">
            <Ionicons 
              name="information-circle" 
              size={20} 
              color={isDark ? '#60a5fa' : '#3b82f6'} 
              style={{ marginTop: 2 }} 
            />
            <View className="flex-1 ml-3">
              <Text className={`text-sm font-medium ${isDark ? 'text-info-200' : 'text-info-800'}`}>
                Delivery Information
              </Text>
              <Text className={`text-sm mt-1 ${isDark ? 'text-info-300' : 'text-info-700'}`}>
                Add multiple addresses for faster checkout. Your default address will be pre-selected during orders.
              </Text>
            </View>
          </View>
        </View>

        {/* Loading State */}
        {isLoading ? (
          <View className="items-center py-12">
            <LoadingSpinner message="Loading addresses..." />
          </View>
        ) : error ? (
          <View className="items-center py-12">
            <Ionicons name="alert-circle" size={48} color="#ef4444" />
            <Text className="text-lg font-semibold mb-2 text-red-600">
              Error Loading Addresses
            </Text>
            <Text className="text-center mb-4 text-neutral-600">
              Something went wrong. Please try again.
            </Text>
            <Button
              title="Retry"
              onPress={() => queryClient.invalidateQueries({ queryKey: ['addresses'] })}
              variant="outline"
              size="small"
            />
          </View>
        ) : addresses.length > 0 ? (
          <FlatList
            data={addresses}
            renderItem={renderAddressItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View className="items-center py-12">
            <View className={`w-20 h-20 rounded-full items-center justify-center mb-4 ${isDark ? 'bg-neutral-800' : 'bg-neutral-100'}`}>
              <Ionicons 
                name="location-outline" 
                size={32} 
                color={isDark ? '#a3a3a3' : '#64748b'} 
              />
            </View>
            <Text className={`text-lg font-semibold mb-2 ${isDark ? 'text-neutral-200' : 'text-neutral-800'}`}>
              No Addresses Added
            </Text>
            <Text className={`text-center mb-6 ${isDark ? 'text-neutral-400' : 'text-neutral-600'}`}>
              Add your first delivery address to get started with orders.
            </Text>
            <Button
              title="Add Address"
              onPress={handleAddAddress}
              variant="primary"
              size="md"
            />
          </View>
        )}

        {/* Add Address Button (when addresses exist) */}
        {addresses.length > 0 && (
          <Button
            title="Add New Address"
            onPress={handleAddAddress}
            variant="outline"
            size="lg"
            className="mt-4"
          />
        )}
      </ScrollView>

      {/* Add/Edit Address Form Modal */}
      {renderAddressForm()}
    </SafeAreaView>
  );
};

export default ManageAddressesScreen;
