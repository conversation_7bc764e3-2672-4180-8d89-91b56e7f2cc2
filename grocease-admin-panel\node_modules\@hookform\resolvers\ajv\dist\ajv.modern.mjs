import{validateFieldsNatively as e,toNestErrors as r}from"@hookform/resolvers";import s from"ajv";import o from"ajv-errors";import{appendErrors as a}from"react-hook-form";const t=(e,r)=>(e.forEach(e=>{"required"===e.keyword&&(e.instancePath+="/"+e.params.missingProperty)}),e.reduce((e,s)=>{const o=s.instancePath.substring(1).replace(/\//g,".");if(e[o]||(e[o]={message:s.message,type:s.keyword}),r){const t=e[o].types,i=t&&t[s.keyword];e[o]=a(o,r,e,s.keyword,i?[].concat(i,s.message||""):s.message)}return e},{})),i=(a,i,n={})=>async(c,m,l)=>{const d=new s(Object.assign({},{allErrors:!0,validateSchema:!0},i));o(d);const p=d.compile(Object.assign({$async:n&&"async"===n.mode},a)),y=p(c);return l.shouldUseNativeValidation&&e({},l),y?{values:c,errors:{}}:{values:{},errors:r(t(p.errors,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)}};export{i as ajvResolver};
//# sourceMappingURL=ajv.modern.mjs.map
