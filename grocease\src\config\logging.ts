/**
 * Logging configuration for the mobile app
 * Centralized place to control all logging behavior
 */

import { apiLogger, LogLevel } from '../utils/apiLogger';

// Development vs Production logging configuration
const isDevelopment = __DEV__;

export const LoggingConfig = {
  // API Logging
  api: {
    enabled: isDevelopment,
    level: isDevelopment ? LogLevel.VERBOSE : LogLevel.ERROR,
    logRequests: isDevelopment,
    logResponses: isDevelopment,
    logErrors: true, // Always log errors
    logTiming: isDevelopment,
    maxResponseLength: isDevelopment ? 2000 : 500,
  },

  // Auth Logging
  auth: {
    enabled: true, // Always enabled for debugging login issues
    logAttempts: true,
    logSuccess: true,
    logFailures: true,
  },

  // Navigation Logging
  navigation: {
    enabled: isDevelopment,
    logStateChanges: isDevelopment,
    logScreenTransitions: isDevelopment,
  },

  // General App Logging
  app: {
    enabled: isDevelopment,
    logStateChanges: isDevelopment,
    logUserActions: isDevelopment,
  }
};

// Initialize API logger with configuration
export const initializeLogging = () => {
  apiLogger.configure({
    enabled: LoggingConfig.api.enabled,
    level: LoggingConfig.api.level,
    logRequests: LoggingConfig.api.logRequests,
    logResponses: LoggingConfig.api.logResponses,
    logErrors: LoggingConfig.api.logErrors,
    logTiming: LoggingConfig.api.logTiming,
    maxResponseLength: LoggingConfig.api.maxResponseLength,
  });

  if (isDevelopment) {
    console.log('🔧 [LOGGING] Configuration initialized:', {
      environment: isDevelopment ? 'development' : 'production',
      apiLogging: LoggingConfig.api.enabled,
      authLogging: LoggingConfig.auth.enabled,
      navigationLogging: LoggingConfig.navigation.enabled,
    });
  }
};

// Quick logging control functions
export const enableVerboseLogging = () => {
  apiLogger.enableVerboseLogging();
  console.log('🔧 [LOGGING] Verbose logging enabled');
};

export const enableErrorsOnly = () => {
  apiLogger.enableErrorsOnly();
  console.log('🔧 [LOGGING] Error-only logging enabled');
};

export const disableAllLogging = () => {
  apiLogger.disableLogging();
  console.log('🔧 [LOGGING] All logging disabled');
};

// Conditional logging helpers
export const logAuth = (message: string, data?: any) => {
  if (LoggingConfig.auth.enabled) {
    console.log(`🔐 [AUTH] ${message}`, data);
  }
};

export const logNavigation = (message: string, data?: any) => {
  if (LoggingConfig.navigation.enabled) {
    console.log(`🧭 [NAV] ${message}`, data);
  }
};

export const logApp = (message: string, data?: any) => {
  if (LoggingConfig.app.enabled) {
    console.log(`📱 [APP] ${message}`, data);
  }
};

// Export for global access
export { apiLogger };
