package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.discount.CreateDiscountCodeRequest;
import com.grocease.dto.discount.DiscountCodeDto;
import com.grocease.service.DiscountCodeService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/discount-codes")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AdminDiscountCodeController {

    private final DiscountCodeService discountCodeService;

    @PostMapping
    public ResponseEntity<ApiResponse<DiscountCodeDto>> createDiscountCode(
            @Valid @RequestBody CreateDiscountCodeRequest request) {
        log.info("Admin creating discount code: {}", request.getCode());
        DiscountCodeDto discountCode = discountCodeService.createDiscountCode(request);
        return ResponseEntity.ok(ApiResponse.success(discountCode, "Discount code created successfully"));
    }

    @GetMapping
    public ResponseEntity<PaginatedResponse<DiscountCodeDto>> getAllDiscountCodes(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) Boolean activeOnly) {

        // Validate pagination parameters at controller level (1-based)
        page = Math.max(1, page);
        limit = Math.max(1, Math.min(100, limit));

        log.info("Admin getting discount codes - page: {}, limit: {}, activeOnly: {}", page, limit, activeOnly);

        PaginatedResponse<DiscountCodeDto> response = discountCodeService.getAllDiscountCodes(page, limit, activeOnly);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<DiscountCodeDto>> getDiscountCodeById(@PathVariable Long id) {
        log.info("Admin getting discount code by id: {}", id);
        DiscountCodeDto discountCode = discountCodeService.getDiscountCodeById(id);
        return ResponseEntity.ok(ApiResponse.success(discountCode, "Discount code retrieved successfully"));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<DiscountCodeDto>> updateDiscountCode(
            @PathVariable Long id,
            @Valid @RequestBody CreateDiscountCodeRequest request) {
        log.info("Admin updating discount code: {}", id);
        DiscountCodeDto discountCode = discountCodeService.updateDiscountCode(id, request);
        return ResponseEntity.ok(ApiResponse.success(discountCode, "Discount code updated successfully"));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteDiscountCode(@PathVariable Long id) {
        log.info("Admin deleting discount code: {}", id);
        discountCodeService.deleteDiscountCode(id);
        return ResponseEntity.ok(ApiResponse.success("Discount code deleted successfully", 
                "The discount code has been removed"));
    }

    @PostMapping("/{id}/toggle-status")
    public ResponseEntity<ApiResponse<DiscountCodeDto>> toggleDiscountCodeStatus(@PathVariable Long id) {
        log.info("Admin toggling discount code status: {}", id);
        
        DiscountCodeDto discountCode = discountCodeService.getDiscountCodeById(id);
        
        // Create update request with toggled status
        CreateDiscountCodeRequest updateRequest = CreateDiscountCodeRequest.builder()
                .code(discountCode.getCode())
                .name(discountCode.getName())
                .description(discountCode.getDescription())
                .type(discountCode.getType())
                .value(discountCode.getValue())
                .minimumOrderAmount(discountCode.getMinimumOrderAmount())
                .maximumDiscountAmount(discountCode.getMaximumDiscountAmount())
                .usageLimit(discountCode.getUsageLimit())
                .usageLimitPerUser(discountCode.getUsageLimitPerUser())
                .validFrom(discountCode.getValidFrom())
                .validUntil(discountCode.getValidUntil())
                .isActive(!discountCode.getIsActive()) // Toggle status
                .isFirstOrderOnly(discountCode.getIsFirstOrderOnly())
                .build();
        
        DiscountCodeDto updatedCode = discountCodeService.updateDiscountCode(id, updateRequest);
        return ResponseEntity.ok(ApiResponse.success(updatedCode, "Discount code status updated successfully"));
    }
}
