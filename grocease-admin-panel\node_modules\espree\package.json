{"name": "espree", "description": "An Esprima-compatible JavaScript parser built on Acorn", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/eslint/espree", "main": "dist/espree.cjs", "type": "module", "exports": {".": [{"import": "./espree.js", "require": "./dist/espree.cjs", "default": "./dist/espree.cjs"}, "./dist/espree.cjs"], "./package.json": "./package.json"}, "version": "9.6.1", "files": ["lib", "dist/espree.cjs", "espree.js"], "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "repository": "eslint/espree", "bugs": {"url": "https://github.com/eslint/espree/issues"}, "funding": "https://opencollective.com/eslint", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.2.0", "c8": "^7.11.0", "chai": "^4.3.6", "eslint": "^8.44.0", "eslint-config-eslint": "^8.0.0", "eslint-plugin-n": "^16.0.0", "eslint-release": "^3.2.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "globals": "^13.20.0", "lint-staged": "^13.2.0", "mocha": "^9.2.2", "npm-run-all": "^4.1.5", "rollup": "^2.41.2", "shelljs": "^0.3.0", "yorkie": "^2.0.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax", "acorn"], "gitHooks": {"pre-commit": "lint-staged"}, "scripts": {"unit": "npm-run-all -s unit:*", "unit:esm": "c8 mocha --color --reporter progress --timeout 30000 'tests/lib/**/*.js'", "unit:cjs": "mocha --color --reporter progress --timeout 30000 tests/lib/commonjs.cjs", "test": "npm-run-all -p unit lint", "lint": "eslint .  --report-unused-disable-directives", "fixlint": "npm run lint -- --fix", "build": "rollup -c rollup.config.js", "build:debug": "npm run build -- -m", "update-version": "node tools/update-version.js", "pretest": "npm run build", "prepublishOnly": "npm run update-version && npm run build", "sync-docs": "node sync-docs.js", "generate-release": "eslint-generate-release", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "publish-release": "eslint-publish-release"}}