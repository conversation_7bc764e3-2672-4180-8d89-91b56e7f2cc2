# 📊 API Logging System

This mobile app now has comprehensive API logging to help debug network requests and authentication issues.

## 🚀 Features

### ✅ **What Gets Logged:**
- **All API Requests**: Method, URL, headers, body (sanitized)
- **All API Responses**: Status, data, timing
- **Network Errors**: Timeouts, connection failures
- **Authentication Events**: Login/register attempts and results
- **Navigation Changes**: Auth state transitions

### 🔒 **Security:**
- **Sensitive Data Protection**: Passwords, tokens, and secrets are automatically redacted
- **Configurable Levels**: Different logging levels for development vs production

## 📱 **How to Use**

### **View Logs in Development:**
1. Open React Native debugger or Metro console
2. Look for these log prefixes:
   - `🚀 [API REQUEST]` - Outgoing API calls
   - `✅ [API SUCCESS]` - Successful responses  
   - `❌ [API ERROR]` - Failed responses
   - `💥 [API NETWORK ERROR]` - Network issues
   - `🔐 [AUTH]` - Authentication events
   - `🧭 [NAV]` - Navigation changes

### **Example Log Output:**
```
🚀 [API REQUEST abc123] {
  method: "POST",
  url: "http://*************:8080/api/auth/login",
  headers: { "Authorization": "Bearer [REDACTED]" },
  body: { "email": "<EMAIL>", "password": "[REDACTED]" }
}

✅ [API SUCCESS abc123] {
  status: 200,
  duration: "245ms",
  responseSize: "1.2KB"
}

🔐 [AUTH] LOGIN {
  email: "<EMAIL>",
  success: true,
  hasToken: true
}
```

## ⚙️ **Configuration**

### **Quick Controls:**
```typescript
import { enableVerboseLogging, enableErrorsOnly, disableAllLogging } from './src/config/logging';

// Enable all logging (development mode)
enableVerboseLogging();

// Only log errors (production mode)
enableErrorsOnly();

// Disable all logging
disableAllLogging();
```

### **Custom Configuration:**
Edit `src/config/logging.ts` to customize:
- Log levels (NONE, ERROR, WARN, INFO, DEBUG, VERBOSE)
- What types of requests to log
- Maximum response size to display
- Sensitive fields to redact

## 🐛 **Debugging Login Issues**

With this logging system, you can now easily debug login problems:

1. **Check API Connectivity**: Look for `🚀 [API REQUEST]` logs
2. **Verify Request Data**: Ensure email/password are being sent correctly
3. **Check Response**: Look for `✅ [API SUCCESS]` or `❌ [API ERROR]` 
4. **Monitor Auth State**: Watch `🔐 [AUTH]` and `🧭 [NAV]` logs
5. **Track Navigation**: See if auth state changes trigger navigation

## 📋 **Log Levels**

- **VERBOSE (5)**: Everything including request/response bodies
- **DEBUG (4)**: API calls with basic info
- **INFO (3)**: Important events (auth, navigation)
- **WARN (2)**: Warnings and potential issues
- **ERROR (1)**: Only errors and failures
- **NONE (0)**: No logging

## 🔧 **Development vs Production**

- **Development**: Verbose logging enabled by default
- **Production**: Only errors logged to avoid performance impact
- **Automatic**: Switches based on `__DEV__` flag

## 💡 **Tips**

1. **Filter Logs**: Use browser/debugger search to filter by log type (e.g., search "[AUTH]")
2. **Request IDs**: Each API call has a unique ID to track request/response pairs
3. **Timing**: All requests show duration to identify slow calls
4. **Sanitization**: Sensitive data is automatically hidden but structure is preserved

This logging system will help you quickly identify and fix any API or authentication issues! 🎯
