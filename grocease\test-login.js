// Simple test script to verify login API works
const fetch = require('node-fetch');

const API_BASE_URL = 'http://*************:8080/api';

async function testLogin() {
  console.log('Testing login API...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'ashish123' // Try common passwords
      })
    });

    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ Login successful!');
      console.log('User:', data.data.user.email);
      console.log('Token received:', data.data.token ? 'Yes' : 'No');
    } else {
      console.log('❌ Login failed:', data.message);
    }
    
  } catch (error) {
    console.error('❌ Network error:', error.message);
  }
}

// Test with different credentials
async function testMultipleCredentials() {
  const credentials = [
    { email: '<EMAIL>', password: 'ashish123' },
    { email: '<EMAIL>', password: 'ashish123' },
    { email: '<EMAIL>', password: 'admin123' },
    { email: '<EMAIL>', password: 'test123' }
  ];
  
  for (const cred of credentials) {
    console.log(`\n--- Testing ${cred.email} ---`);
    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cred)
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`✅ SUCCESS: ${cred.email}`);
        return cred; // Return working credentials
      } else {
        console.log(`❌ FAILED: ${cred.email} - ${data.message}`);
      }
    } catch (error) {
      console.log(`❌ ERROR: ${cred.email} - ${error.message}`);
    }
  }
  
  return null;
}

// Run tests
testMultipleCredentials().then(workingCreds => {
  if (workingCreds) {
    console.log('\n🎉 Working credentials found:', workingCreds);
  } else {
    console.log('\n😞 No working credentials found');
  }
});
