"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-time-format";
exports.ids = ["vendor-chunks/d3-time-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-time-format/src/defaultLocale.js":
/*!**********************************************************!*\
  !*** ./node_modules/d3-time-format/src/defaultLocale.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultLocale),\n/* harmony export */   timeFormat: () => (/* binding */ timeFormat),\n/* harmony export */   timeParse: () => (/* binding */ timeParse),\n/* harmony export */   utcFormat: () => (/* binding */ utcFormat),\n/* harmony export */   utcParse: () => (/* binding */ utcParse)\n/* harmony export */ });\n/* harmony import */ var _locale_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./locale.js */ \"(ssr)/./node_modules/d3-time-format/src/locale.js\");\n\nvar locale;\nvar timeFormat;\nvar timeParse;\nvar utcFormat;\nvar utcParse;\ndefaultLocale({\n    dateTime: \"%x, %X\",\n    date: \"%-m/%-d/%Y\",\n    time: \"%-I:%M:%S %p\",\n    periods: [\n        \"AM\",\n        \"PM\"\n    ],\n    days: [\n        \"Sunday\",\n        \"Monday\",\n        \"Tuesday\",\n        \"Wednesday\",\n        \"Thursday\",\n        \"Friday\",\n        \"Saturday\"\n    ],\n    shortDays: [\n        \"Sun\",\n        \"Mon\",\n        \"Tue\",\n        \"Wed\",\n        \"Thu\",\n        \"Fri\",\n        \"Sat\"\n    ],\n    months: [\n        \"January\",\n        \"February\",\n        \"March\",\n        \"April\",\n        \"May\",\n        \"June\",\n        \"July\",\n        \"August\",\n        \"September\",\n        \"October\",\n        \"November\",\n        \"December\"\n    ],\n    shortMonths: [\n        \"Jan\",\n        \"Feb\",\n        \"Mar\",\n        \"Apr\",\n        \"May\",\n        \"Jun\",\n        \"Jul\",\n        \"Aug\",\n        \"Sep\",\n        \"Oct\",\n        \"Nov\",\n        \"Dec\"\n    ]\n});\nfunction defaultLocale(definition) {\n    locale = (0,_locale_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(definition);\n    timeFormat = locale.format;\n    timeParse = locale.parse;\n    utcFormat = locale.utcFormat;\n    utcParse = locale.utcParse;\n    return locale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time-format/src/defaultLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time-format/src/locale.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-time-format/src/locale.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ formatLocale)\n/* harmony export */ });\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/week.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/day.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/year.js\");\n\nfunction localDate(d) {\n    if (0 <= d.y && d.y < 100) {\n        var date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);\n        date.setFullYear(d.y);\n        return date;\n    }\n    return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);\n}\nfunction utcDate(d) {\n    if (0 <= d.y && d.y < 100) {\n        var date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));\n        date.setUTCFullYear(d.y);\n        return date;\n    }\n    return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));\n}\nfunction newDate(y, m, d) {\n    return {\n        y: y,\n        m: m,\n        d: d,\n        H: 0,\n        M: 0,\n        S: 0,\n        L: 0\n    };\n}\nfunction formatLocale(locale) {\n    var locale_dateTime = locale.dateTime, locale_date = locale.date, locale_time = locale.time, locale_periods = locale.periods, locale_weekdays = locale.days, locale_shortWeekdays = locale.shortDays, locale_months = locale.months, locale_shortMonths = locale.shortMonths;\n    var periodRe = formatRe(locale_periods), periodLookup = formatLookup(locale_periods), weekdayRe = formatRe(locale_weekdays), weekdayLookup = formatLookup(locale_weekdays), shortWeekdayRe = formatRe(locale_shortWeekdays), shortWeekdayLookup = formatLookup(locale_shortWeekdays), monthRe = formatRe(locale_months), monthLookup = formatLookup(locale_months), shortMonthRe = formatRe(locale_shortMonths), shortMonthLookup = formatLookup(locale_shortMonths);\n    var formats = {\n        \"a\": formatShortWeekday,\n        \"A\": formatWeekday,\n        \"b\": formatShortMonth,\n        \"B\": formatMonth,\n        \"c\": null,\n        \"d\": formatDayOfMonth,\n        \"e\": formatDayOfMonth,\n        \"f\": formatMicroseconds,\n        \"g\": formatYearISO,\n        \"G\": formatFullYearISO,\n        \"H\": formatHour24,\n        \"I\": formatHour12,\n        \"j\": formatDayOfYear,\n        \"L\": formatMilliseconds,\n        \"m\": formatMonthNumber,\n        \"M\": formatMinutes,\n        \"p\": formatPeriod,\n        \"q\": formatQuarter,\n        \"Q\": formatUnixTimestamp,\n        \"s\": formatUnixTimestampSeconds,\n        \"S\": formatSeconds,\n        \"u\": formatWeekdayNumberMonday,\n        \"U\": formatWeekNumberSunday,\n        \"V\": formatWeekNumberISO,\n        \"w\": formatWeekdayNumberSunday,\n        \"W\": formatWeekNumberMonday,\n        \"x\": null,\n        \"X\": null,\n        \"y\": formatYear,\n        \"Y\": formatFullYear,\n        \"Z\": formatZone,\n        \"%\": formatLiteralPercent\n    };\n    var utcFormats = {\n        \"a\": formatUTCShortWeekday,\n        \"A\": formatUTCWeekday,\n        \"b\": formatUTCShortMonth,\n        \"B\": formatUTCMonth,\n        \"c\": null,\n        \"d\": formatUTCDayOfMonth,\n        \"e\": formatUTCDayOfMonth,\n        \"f\": formatUTCMicroseconds,\n        \"g\": formatUTCYearISO,\n        \"G\": formatUTCFullYearISO,\n        \"H\": formatUTCHour24,\n        \"I\": formatUTCHour12,\n        \"j\": formatUTCDayOfYear,\n        \"L\": formatUTCMilliseconds,\n        \"m\": formatUTCMonthNumber,\n        \"M\": formatUTCMinutes,\n        \"p\": formatUTCPeriod,\n        \"q\": formatUTCQuarter,\n        \"Q\": formatUnixTimestamp,\n        \"s\": formatUnixTimestampSeconds,\n        \"S\": formatUTCSeconds,\n        \"u\": formatUTCWeekdayNumberMonday,\n        \"U\": formatUTCWeekNumberSunday,\n        \"V\": formatUTCWeekNumberISO,\n        \"w\": formatUTCWeekdayNumberSunday,\n        \"W\": formatUTCWeekNumberMonday,\n        \"x\": null,\n        \"X\": null,\n        \"y\": formatUTCYear,\n        \"Y\": formatUTCFullYear,\n        \"Z\": formatUTCZone,\n        \"%\": formatLiteralPercent\n    };\n    var parses = {\n        \"a\": parseShortWeekday,\n        \"A\": parseWeekday,\n        \"b\": parseShortMonth,\n        \"B\": parseMonth,\n        \"c\": parseLocaleDateTime,\n        \"d\": parseDayOfMonth,\n        \"e\": parseDayOfMonth,\n        \"f\": parseMicroseconds,\n        \"g\": parseYear,\n        \"G\": parseFullYear,\n        \"H\": parseHour24,\n        \"I\": parseHour24,\n        \"j\": parseDayOfYear,\n        \"L\": parseMilliseconds,\n        \"m\": parseMonthNumber,\n        \"M\": parseMinutes,\n        \"p\": parsePeriod,\n        \"q\": parseQuarter,\n        \"Q\": parseUnixTimestamp,\n        \"s\": parseUnixTimestampSeconds,\n        \"S\": parseSeconds,\n        \"u\": parseWeekdayNumberMonday,\n        \"U\": parseWeekNumberSunday,\n        \"V\": parseWeekNumberISO,\n        \"w\": parseWeekdayNumberSunday,\n        \"W\": parseWeekNumberMonday,\n        \"x\": parseLocaleDate,\n        \"X\": parseLocaleTime,\n        \"y\": parseYear,\n        \"Y\": parseFullYear,\n        \"Z\": parseZone,\n        \"%\": parseLiteralPercent\n    };\n    // These recursive directive definitions must be deferred.\n    formats.x = newFormat(locale_date, formats);\n    formats.X = newFormat(locale_time, formats);\n    formats.c = newFormat(locale_dateTime, formats);\n    utcFormats.x = newFormat(locale_date, utcFormats);\n    utcFormats.X = newFormat(locale_time, utcFormats);\n    utcFormats.c = newFormat(locale_dateTime, utcFormats);\n    function newFormat(specifier, formats) {\n        return function(date) {\n            var string = [], i = -1, j = 0, n = specifier.length, c, pad, format;\n            if (!(date instanceof Date)) date = new Date(+date);\n            while(++i < n){\n                if (specifier.charCodeAt(i) === 37) {\n                    string.push(specifier.slice(j, i));\n                    if ((pad = pads[c = specifier.charAt(++i)]) != null) c = specifier.charAt(++i);\n                    else pad = c === \"e\" ? \" \" : \"0\";\n                    if (format = formats[c]) c = format(date, pad);\n                    string.push(c);\n                    j = i + 1;\n                }\n            }\n            string.push(specifier.slice(j, i));\n            return string.join(\"\");\n        };\n    }\n    function newParse(specifier, Z) {\n        return function(string) {\n            var d = newDate(1900, undefined, 1), i = parseSpecifier(d, specifier, string += \"\", 0), week, day;\n            if (i != string.length) return null;\n            // If a UNIX timestamp is specified, return it.\n            if (\"Q\" in d) return new Date(d.Q);\n            if (\"s\" in d) return new Date(d.s * 1000 + (\"L\" in d ? d.L : 0));\n            // If this is utcParse, never use the local timezone.\n            if (Z && !(\"Z\" in d)) d.Z = 0;\n            // The am-pm flag is 0 for AM, and 1 for PM.\n            if (\"p\" in d) d.H = d.H % 12 + d.p * 12;\n            // If the month was not specified, inherit from the quarter.\n            if (d.m === undefined) d.m = \"q\" in d ? d.q : 0;\n            // Convert day-of-week and week-of-year to day-of-year.\n            if (\"V\" in d) {\n                if (d.V < 1 || d.V > 53) return null;\n                if (!(\"w\" in d)) d.w = 1;\n                if (\"Z\" in d) {\n                    week = utcDate(newDate(d.y, 0, 1)), day = week.getUTCDay();\n                    week = day > 4 || day === 0 ? d3_time__WEBPACK_IMPORTED_MODULE_0__.utcMonday.ceil(week) : (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.utcMonday)(week);\n                    week = d3_time__WEBPACK_IMPORTED_MODULE_1__.utcDay.offset(week, (d.V - 1) * 7);\n                    d.y = week.getUTCFullYear();\n                    d.m = week.getUTCMonth();\n                    d.d = week.getUTCDate() + (d.w + 6) % 7;\n                } else {\n                    week = localDate(newDate(d.y, 0, 1)), day = week.getDay();\n                    week = day > 4 || day === 0 ? d3_time__WEBPACK_IMPORTED_MODULE_0__.timeMonday.ceil(week) : (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.timeMonday)(week);\n                    week = d3_time__WEBPACK_IMPORTED_MODULE_1__.timeDay.offset(week, (d.V - 1) * 7);\n                    d.y = week.getFullYear();\n                    d.m = week.getMonth();\n                    d.d = week.getDate() + (d.w + 6) % 7;\n                }\n            } else if (\"W\" in d || \"U\" in d) {\n                if (!(\"w\" in d)) d.w = \"u\" in d ? d.u % 7 : \"W\" in d ? 1 : 0;\n                day = \"Z\" in d ? utcDate(newDate(d.y, 0, 1)).getUTCDay() : localDate(newDate(d.y, 0, 1)).getDay();\n                d.m = 0;\n                d.d = \"W\" in d ? (d.w + 6) % 7 + d.W * 7 - (day + 5) % 7 : d.w + d.U * 7 - (day + 6) % 7;\n            }\n            // If a time zone is specified, all fields are interpreted as UTC and then\n            // offset according to the specified time zone.\n            if (\"Z\" in d) {\n                d.H += d.Z / 100 | 0;\n                d.M += d.Z % 100;\n                return utcDate(d);\n            }\n            // Otherwise, all fields are in local time.\n            return localDate(d);\n        };\n    }\n    function parseSpecifier(d, specifier, string, j) {\n        var i = 0, n = specifier.length, m = string.length, c, parse;\n        while(i < n){\n            if (j >= m) return -1;\n            c = specifier.charCodeAt(i++);\n            if (c === 37) {\n                c = specifier.charAt(i++);\n                parse = parses[c in pads ? specifier.charAt(i++) : c];\n                if (!parse || (j = parse(d, string, j)) < 0) return -1;\n            } else if (c != string.charCodeAt(j++)) {\n                return -1;\n            }\n        }\n        return j;\n    }\n    function parsePeriod(d, string, i) {\n        var n = periodRe.exec(string.slice(i));\n        return n ? (d.p = periodLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseShortWeekday(d, string, i) {\n        var n = shortWeekdayRe.exec(string.slice(i));\n        return n ? (d.w = shortWeekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseWeekday(d, string, i) {\n        var n = weekdayRe.exec(string.slice(i));\n        return n ? (d.w = weekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseShortMonth(d, string, i) {\n        var n = shortMonthRe.exec(string.slice(i));\n        return n ? (d.m = shortMonthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseMonth(d, string, i) {\n        var n = monthRe.exec(string.slice(i));\n        return n ? (d.m = monthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n    }\n    function parseLocaleDateTime(d, string, i) {\n        return parseSpecifier(d, locale_dateTime, string, i);\n    }\n    function parseLocaleDate(d, string, i) {\n        return parseSpecifier(d, locale_date, string, i);\n    }\n    function parseLocaleTime(d, string, i) {\n        return parseSpecifier(d, locale_time, string, i);\n    }\n    function formatShortWeekday(d) {\n        return locale_shortWeekdays[d.getDay()];\n    }\n    function formatWeekday(d) {\n        return locale_weekdays[d.getDay()];\n    }\n    function formatShortMonth(d) {\n        return locale_shortMonths[d.getMonth()];\n    }\n    function formatMonth(d) {\n        return locale_months[d.getMonth()];\n    }\n    function formatPeriod(d) {\n        return locale_periods[+(d.getHours() >= 12)];\n    }\n    function formatQuarter(d) {\n        return 1 + ~~(d.getMonth() / 3);\n    }\n    function formatUTCShortWeekday(d) {\n        return locale_shortWeekdays[d.getUTCDay()];\n    }\n    function formatUTCWeekday(d) {\n        return locale_weekdays[d.getUTCDay()];\n    }\n    function formatUTCShortMonth(d) {\n        return locale_shortMonths[d.getUTCMonth()];\n    }\n    function formatUTCMonth(d) {\n        return locale_months[d.getUTCMonth()];\n    }\n    function formatUTCPeriod(d) {\n        return locale_periods[+(d.getUTCHours() >= 12)];\n    }\n    function formatUTCQuarter(d) {\n        return 1 + ~~(d.getUTCMonth() / 3);\n    }\n    return {\n        format: function(specifier) {\n            var f = newFormat(specifier += \"\", formats);\n            f.toString = function() {\n                return specifier;\n            };\n            return f;\n        },\n        parse: function(specifier) {\n            var p = newParse(specifier += \"\", false);\n            p.toString = function() {\n                return specifier;\n            };\n            return p;\n        },\n        utcFormat: function(specifier) {\n            var f = newFormat(specifier += \"\", utcFormats);\n            f.toString = function() {\n                return specifier;\n            };\n            return f;\n        },\n        utcParse: function(specifier) {\n            var p = newParse(specifier += \"\", true);\n            p.toString = function() {\n                return specifier;\n            };\n            return p;\n        }\n    };\n}\nvar pads = {\n    \"-\": \"\",\n    \"_\": \" \",\n    \"0\": \"0\"\n}, numberRe = /^\\s*\\d+/, percentRe = /^%/, requoteRe = /[\\\\^$*+?|[\\]().{}]/g;\nfunction pad(value, fill, width) {\n    var sign = value < 0 ? \"-\" : \"\", string = (sign ? -value : value) + \"\", length = string.length;\n    return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);\n}\nfunction requote(s) {\n    return s.replace(requoteRe, \"\\\\$&\");\n}\nfunction formatRe(names) {\n    return new RegExp(\"^(?:\" + names.map(requote).join(\"|\") + \")\", \"i\");\n}\nfunction formatLookup(names) {\n    return new Map(names.map((name, i)=>[\n            name.toLowerCase(),\n            i\n        ]));\n}\nfunction parseWeekdayNumberSunday(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 1));\n    return n ? (d.w = +n[0], i + n[0].length) : -1;\n}\nfunction parseWeekdayNumberMonday(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 1));\n    return n ? (d.u = +n[0], i + n[0].length) : -1;\n}\nfunction parseWeekNumberSunday(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.U = +n[0], i + n[0].length) : -1;\n}\nfunction parseWeekNumberISO(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.V = +n[0], i + n[0].length) : -1;\n}\nfunction parseWeekNumberMonday(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.W = +n[0], i + n[0].length) : -1;\n}\nfunction parseFullYear(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 4));\n    return n ? (d.y = +n[0], i + n[0].length) : -1;\n}\nfunction parseYear(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.y = +n[0] + (+n[0] > 68 ? 1900 : 2000), i + n[0].length) : -1;\n}\nfunction parseZone(d, string, i) {\n    var n = /^(Z)|([+-]\\d\\d)(?::?(\\d\\d))?/.exec(string.slice(i, i + 6));\n    return n ? (d.Z = n[1] ? 0 : -(n[2] + (n[3] || \"00\")), i + n[0].length) : -1;\n}\nfunction parseQuarter(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 1));\n    return n ? (d.q = n[0] * 3 - 3, i + n[0].length) : -1;\n}\nfunction parseMonthNumber(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.m = n[0] - 1, i + n[0].length) : -1;\n}\nfunction parseDayOfMonth(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.d = +n[0], i + n[0].length) : -1;\n}\nfunction parseDayOfYear(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 3));\n    return n ? (d.m = 0, d.d = +n[0], i + n[0].length) : -1;\n}\nfunction parseHour24(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.H = +n[0], i + n[0].length) : -1;\n}\nfunction parseMinutes(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.M = +n[0], i + n[0].length) : -1;\n}\nfunction parseSeconds(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 2));\n    return n ? (d.S = +n[0], i + n[0].length) : -1;\n}\nfunction parseMilliseconds(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 3));\n    return n ? (d.L = +n[0], i + n[0].length) : -1;\n}\nfunction parseMicroseconds(d, string, i) {\n    var n = numberRe.exec(string.slice(i, i + 6));\n    return n ? (d.L = Math.floor(n[0] / 1000), i + n[0].length) : -1;\n}\nfunction parseLiteralPercent(d, string, i) {\n    var n = percentRe.exec(string.slice(i, i + 1));\n    return n ? i + n[0].length : -1;\n}\nfunction parseUnixTimestamp(d, string, i) {\n    var n = numberRe.exec(string.slice(i));\n    return n ? (d.Q = +n[0], i + n[0].length) : -1;\n}\nfunction parseUnixTimestampSeconds(d, string, i) {\n    var n = numberRe.exec(string.slice(i));\n    return n ? (d.s = +n[0], i + n[0].length) : -1;\n}\nfunction formatDayOfMonth(d, p) {\n    return pad(d.getDate(), p, 2);\n}\nfunction formatHour24(d, p) {\n    return pad(d.getHours(), p, 2);\n}\nfunction formatHour12(d, p) {\n    return pad(d.getHours() % 12 || 12, p, 2);\n}\nfunction formatDayOfYear(d, p) {\n    return pad(1 + d3_time__WEBPACK_IMPORTED_MODULE_1__.timeDay.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d), d), p, 3);\n}\nfunction formatMilliseconds(d, p) {\n    return pad(d.getMilliseconds(), p, 3);\n}\nfunction formatMicroseconds(d, p) {\n    return formatMilliseconds(d, p) + \"000\";\n}\nfunction formatMonthNumber(d, p) {\n    return pad(d.getMonth() + 1, p, 2);\n}\nfunction formatMinutes(d, p) {\n    return pad(d.getMinutes(), p, 2);\n}\nfunction formatSeconds(d, p) {\n    return pad(d.getSeconds(), p, 2);\n}\nfunction formatWeekdayNumberMonday(d) {\n    var day = d.getDay();\n    return day === 0 ? 7 : day;\n}\nfunction formatWeekNumberSunday(d, p) {\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.timeSunday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d) - 1, d), p, 2);\n}\nfunction dISO(d) {\n    var day = d.getDay();\n    return day >= 4 || day === 0 ? (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday)(d) : d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday.ceil(d);\n}\nfunction formatWeekNumberISO(d, p) {\n    d = dISO(d);\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d), d) + ((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d).getDay() === 4), p, 2);\n}\nfunction formatWeekdayNumberSunday(d) {\n    return d.getDay();\n}\nfunction formatWeekNumberMonday(d, p) {\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.timeMonday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.timeYear)(d) - 1, d), p, 2);\n}\nfunction formatYear(d, p) {\n    return pad(d.getFullYear() % 100, p, 2);\n}\nfunction formatYearISO(d, p) {\n    d = dISO(d);\n    return pad(d.getFullYear() % 100, p, 2);\n}\nfunction formatFullYear(d, p) {\n    return pad(d.getFullYear() % 10000, p, 4);\n}\nfunction formatFullYearISO(d, p) {\n    var day = d.getDay();\n    d = day >= 4 || day === 0 ? (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday)(d) : d3_time__WEBPACK_IMPORTED_MODULE_0__.timeThursday.ceil(d);\n    return pad(d.getFullYear() % 10000, p, 4);\n}\nfunction formatZone(d) {\n    var z = d.getTimezoneOffset();\n    return (z > 0 ? \"-\" : (z *= -1, \"+\")) + pad(z / 60 | 0, \"0\", 2) + pad(z % 60, \"0\", 2);\n}\nfunction formatUTCDayOfMonth(d, p) {\n    return pad(d.getUTCDate(), p, 2);\n}\nfunction formatUTCHour24(d, p) {\n    return pad(d.getUTCHours(), p, 2);\n}\nfunction formatUTCHour12(d, p) {\n    return pad(d.getUTCHours() % 12 || 12, p, 2);\n}\nfunction formatUTCDayOfYear(d, p) {\n    return pad(1 + d3_time__WEBPACK_IMPORTED_MODULE_1__.utcDay.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d), d), p, 3);\n}\nfunction formatUTCMilliseconds(d, p) {\n    return pad(d.getUTCMilliseconds(), p, 3);\n}\nfunction formatUTCMicroseconds(d, p) {\n    return formatUTCMilliseconds(d, p) + \"000\";\n}\nfunction formatUTCMonthNumber(d, p) {\n    return pad(d.getUTCMonth() + 1, p, 2);\n}\nfunction formatUTCMinutes(d, p) {\n    return pad(d.getUTCMinutes(), p, 2);\n}\nfunction formatUTCSeconds(d, p) {\n    return pad(d.getUTCSeconds(), p, 2);\n}\nfunction formatUTCWeekdayNumberMonday(d) {\n    var dow = d.getUTCDay();\n    return dow === 0 ? 7 : dow;\n}\nfunction formatUTCWeekNumberSunday(d, p) {\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.utcSunday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d) - 1, d), p, 2);\n}\nfunction UTCdISO(d) {\n    var day = d.getUTCDay();\n    return day >= 4 || day === 0 ? (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday)(d) : d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday.ceil(d);\n}\nfunction formatUTCWeekNumberISO(d, p) {\n    d = UTCdISO(d);\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d), d) + ((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d).getUTCDay() === 4), p, 2);\n}\nfunction formatUTCWeekdayNumberSunday(d) {\n    return d.getUTCDay();\n}\nfunction formatUTCWeekNumberMonday(d, p) {\n    return pad(d3_time__WEBPACK_IMPORTED_MODULE_0__.utcMonday.count((0,d3_time__WEBPACK_IMPORTED_MODULE_2__.utcYear)(d) - 1, d), p, 2);\n}\nfunction formatUTCYear(d, p) {\n    return pad(d.getUTCFullYear() % 100, p, 2);\n}\nfunction formatUTCYearISO(d, p) {\n    d = UTCdISO(d);\n    return pad(d.getUTCFullYear() % 100, p, 2);\n}\nfunction formatUTCFullYear(d, p) {\n    return pad(d.getUTCFullYear() % 10000, p, 4);\n}\nfunction formatUTCFullYearISO(d, p) {\n    var day = d.getUTCDay();\n    d = day >= 4 || day === 0 ? (0,d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday)(d) : d3_time__WEBPACK_IMPORTED_MODULE_0__.utcThursday.ceil(d);\n    return pad(d.getUTCFullYear() % 10000, p, 4);\n}\nfunction formatUTCZone() {\n    return \"+0000\";\n}\nfunction formatLiteralPercent() {\n    return \"%\";\n}\nfunction formatUnixTimestamp(d) {\n    return +d;\n}\nfunction formatUnixTimestampSeconds(d) {\n    return Math.floor(+d / 1000);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time-format/src/locale.js\n");

/***/ })

};
;